{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to MySQL server\u001b[39m","timestamp":"2025-05-31 11:49:26:4926"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase 'sprint_management' created or already exists\u001b[39m","timestamp":"2025-05-31 11:49:26:4926"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to MySQL server\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase 'sprint_management' created or already exists\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to database 'sprint_management'\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 1/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 2/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 3/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 4/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 5/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 6/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 7/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 8/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 9/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 10/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 11/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:56:4956"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 11:54:20:5420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 11:54:20:5420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 11:54:20:5420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 11:54:20:5420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 11:54:56:5456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 11:54:56:5456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 11:54:56:5456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 11:54:56:5456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 11:55:18:5518"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 11:55:18:5518"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 11:55:18:5518"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 11:55:18:5518"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 12:48:34:4834"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize email service: nodemailer.createTransporter is not a function\u001b[39m","stack":"TypeError: nodemailer.createTransporter is not a function\n    at EmailService.initialize (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\services\\emailService.js:23:37)\n    at startServer (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\server.js:156:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-05-31 12:48:34:4834"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 12:48:34:4834"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 12:48:34:4834"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 12:48:34:4834"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-05-31 12:50:06:506"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 12:50:26:5026"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize email service: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\u001b[39m\n\u001b[31m535 5.7.8  https://support.google.com/mail/?p=BadCredentials d9443c01a7336-23506cd3407sm38119465ad.141 - gsmtp\u001b[39m","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d9443c01a7336-23506cd3407sm38119465ad.141 - gsmtp","responseCode":535,"stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d9443c01a7336-23506cd3407sm38119465ad.141 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-05-31 12:50:27:5027"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 12:50:28:5028"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 12:50:28:5028"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 12:50:28:5028"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 12:51:08:518"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize email service: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\u001b[39m\n\u001b[31m535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-3124e2b67ffsm2441117a91.5 - gsmtp\u001b[39m","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-3124e2b67ffsm2441117a91.5 - gsmtp","responseCode":535,"stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-3124e2b67ffsm2441117a91.5 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-05-31 12:51:10:5110"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 12:51:10:5110"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 12:51:10:5110"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 12:51:10:5110"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mSIGINT received, shutting down gracefully\u001b[39m","timestamp":"2025-05-31 12:51:53:5153"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 12:52:09:529"}
{"error":"Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d9443c01a7336-23506bc86d9sm38231105ad.24 - gsmtp","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mEmail service verification failed in development mode. Email features will be disabled.\u001b[39m","timestamp":"2025-05-31 12:52:10:5210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 12:52:10:5210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 12:52:10:5210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 12:52:10:5210"}
