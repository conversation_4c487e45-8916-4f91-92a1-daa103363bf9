{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to MySQL server\u001b[39m","timestamp":"2025-05-31 11:49:26:4926"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase 'sprint_management' created or already exists\u001b[39m","timestamp":"2025-05-31 11:49:26:4926"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to MySQL server\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase 'sprint_management' created or already exists\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mConnected to database 'sprint_management'\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 1/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 2/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 3/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 4/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 5/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 6/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 7/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 8/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 9/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 10/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:55:4955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTable 11/11 created successfully\u001b[39m","timestamp":"2025-05-31 11:49:56:4956"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 11:54:20:5420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 11:54:20:5420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 11:54:20:5420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 11:54:20:5420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 11:54:56:5456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 11:54:56:5456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 11:54:56:5456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 11:54:56:5456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-05-31 11:55:18:5518"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3000 in development mode\u001b[39m","timestamp":"2025-05-31 11:55:18:5518"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHealth check available at http://localhost:3000/health\u001b[39m","timestamp":"2025-05-31 11:55:18:5518"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI available at http://localhost:3000/api/v1\u001b[39m","timestamp":"2025-05-31 11:55:18:5518"}
