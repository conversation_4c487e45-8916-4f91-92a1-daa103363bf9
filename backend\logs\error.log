{"code": "ER_UNSUPPORTED_PS", "errno": 1295, "level": "\u001b[31<PERSON><PERSON>r\u001b[39m", "message": "\u001b[31mDatabase migration failed: This command is not supported in the prepared statement protocol yet\u001b[39m", "sql": "USE `sprint_management`", "sqlMessage": "This command is not supported in the prepared statement protocol yet", "sqlState": "HY000", "stack": "Error: This command is not supported in the prepared statement protocol yet\n    at PromiseConnection.execute (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at createDatabase (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\scripts\\migrate.js:24:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)", "timestamp": "2025-05-31 11:49:26:4926"}