{"code":"ER_UNSUPPORTED_PS","errno":1295,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase migration failed: This command is not supported in the prepared statement protocol yet\u001b[39m","sql":"USE `sprint_management`","sqlMessage":"This command is not supported in the prepared statement protocol yet","sqlState":"HY000","stack":"Error: This command is not supported in the prepared statement protocol yet\n    at PromiseConnection.execute (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at createDatabase (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\scripts\\migrate.js:24:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-05-31 11:49:26:4926"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize email service: nodemailer.createTransporter is not a function\u001b[39m","stack":"TypeError: nodemailer.createTransporter is not a function\n    at EmailService.initialize (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\services\\emailService.js:23:37)\n    at startServer (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\server.js:156:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-05-31 12:48:34:4834"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize email service: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\u001b[39m\n\u001b[31m535 5.7.8  https://support.google.com/mail/?p=BadCredentials d9443c01a7336-23506cd3407sm38119465ad.141 - gsmtp\u001b[39m","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d9443c01a7336-23506cd3407sm38119465ad.141 - gsmtp","responseCode":535,"stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d9443c01a7336-23506cd3407sm38119465ad.141 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-05-31 12:50:27:5027"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize email service: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\u001b[39m\n\u001b[31m535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-3124e2b67ffsm2441117a91.5 - gsmtp\u001b[39m","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-3124e2b67ffsm2441117a91.5 - gsmtp","responseCode":535,"stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-3124e2b67ffsm2441117a91.5 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\ai-sprint-management-app\\backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-05-31 12:51:10:5110"}
